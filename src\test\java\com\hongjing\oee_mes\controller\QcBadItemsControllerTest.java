package com.hongjing.oee_mes.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.hongjing.oee_mes.domain.QcBadItem;
import com.hongjing.oee_mes.service.OeeService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(OeeController.class)
public class QcBadItemsControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private OeeService oeeService;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    public void testGetQcBadItemsByLbIds() throws Exception {
        // 准备测试数据
        List<String> lbIds = Arrays.asList(
            "38364648-001 51897217",
            "38364648-001 51897218"
        );

        List<QcBadItem> mockResponse = Arrays.asList(
            new QcBadItem("MO202507090001", "SMT1-1", "38364648-001 51897217", 
                         "SOLDER_BRIDGE", "U1", "严重"),
            new QcBadItem("MO202507090001", "SMT1-1", "38364648-001 51897218", 
                         "MISSING_COMPONENT", "R5", "一般")
        );

        // 模拟服务层返回
        when(oeeService.getQcBadItemsByLbIds(any(List.class))).thenReturn(mockResponse);

        // 执行测试
        mockMvc.perform(post("/oee/qc-bad-items")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(lbIds)))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(2))
                .andExpect(jsonPath("$[0].mo").value("MO202507090001"))
                .andExpect(jsonPath("$[0].plId").value("SMT1-1"))
                .andExpect(jsonPath("$[0].lbId").value("38364648-001 51897217"))
                .andExpect(jsonPath("$[0].badItemId").value("SOLDER_BRIDGE"))
                .andExpect(jsonPath("$[0].badPoint").value("U1"))
                .andExpect(jsonPath("$[0].fieldEx2").value("严重"))
                .andExpect(jsonPath("$[1].mo").value("MO202507090001"))
                .andExpect(jsonPath("$[1].plId").value("SMT1-1"))
                .andExpect(jsonPath("$[1].lbId").value("38364648-001 51897218"))
                .andExpect(jsonPath("$[1].badItemId").value("MISSING_COMPONENT"))
                .andExpect(jsonPath("$[1].badPoint").value("R5"))
                .andExpect(jsonPath("$[1].fieldEx2").value("一般"));
    }

    @Test
    public void testGetQcBadItemsByLbIds_EmptyList() throws Exception {
        // 准备空的测试数据
        List<String> emptyLbIds = Arrays.asList();
        List<QcBadItem> emptyResponse = Arrays.asList();

        // 模拟服务层返回空列表
        when(oeeService.getQcBadItemsByLbIds(any(List.class))).thenReturn(emptyResponse);

        // 执行测试
        mockMvc.perform(post("/oee/qc-bad-items")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(emptyLbIds)))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(0));
    }
}
