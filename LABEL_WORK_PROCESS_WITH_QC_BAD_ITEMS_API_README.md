# 标签工艺过程分析与QC不良项目组合接口文档

## 接口概述
该接口组合了标签工艺过程分析和QC不良项目查询功能。首先根据线体ID和日期范围查询失败的标签工艺过程分析数据，然后根据这些标签的LB_ID查询对应的QC不良项目信息，最终返回组合后的完整数据。

## 接口信息
- **URL**: `/oee-mes-server/oee/label-work-process-analysis-with-qc-bad-items`
- **方法**: GET
- **Content-Type**: application/json

## 请求参数
| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| plId | String | 是 | 线体ID | SMT1-1 |
| startDate | String | 是 | 开始日期时间 | 2025-07-08 08:00:00 |
| endDate | String | 是 | 结束日期时间 | 2025-07-09 08:00:00 |

## 请求示例
```
GET /oee/label-work-process-analysis-with-qc-bad-items?plId=SMT1-1&startDate=2025-07-08 08:00:00&endDate=2025-07-09 08:00:00
```

## 响应格式
```json
[
    {
        "lbId": "38364648-001 51897217",
        "plId": "SMT1-1",
        "mo": "MO202507090001",
        "firstResult": "N",
        "keyWpFirstResult": "N",
        "finalResult": "N",
        "finalWpCmpDate": "2025-07-08T10:30:00.000+00:00",
        "qcBadItems": [
            {
                "mo": "MO202507090001",
                "plId": "SMT1-1",
                "lbId": "38364648-001 51897217",
                "badItemId": "SOLDER_BRIDGE",
                "badPoint": "U1",
                "fieldEx2": "严重"
            },
            {
                "mo": "MO202507090001",
                "plId": "SMT1-1",
                "lbId": "38364648-001 51897217",
                "badItemId": "MISSING_COMPONENT",
                "badPoint": "R5",
                "fieldEx2": "一般"
            }
        ]
    },
    {
        "lbId": "38364648-001 51897218",
        "plId": "SMT1-1",
        "mo": "MO202507090001",
        "firstResult": "N",
        "keyWpFirstResult": "Y",
        "finalResult": "N",
        "finalWpCmpDate": "2025-07-08T11:15:00.000+00:00",
        "qcBadItems": []
    }
]
```

## 响应字段说明

### 主要字段（来自标签工艺过程分析）
| 字段名 | 类型 | 描述 |
|--------|------|------|
| lbId | String | 标签ID |
| plId | String | 线体ID |
| mo | String | 制造订单号 |
| firstResult | String | 首次结果（所有工序的最小IS_PASS值） |
| keyWpFirstResult | String | 关键工序首次结果（仅考虑SMT-03和SMT-06工序的最小IS_PASS值） |
| finalResult | String | 最终结果（按完成时间排序的最后一个IS_PASS值） |
| finalWpCmpDate | Date | 最终工序完成时间 |
| qcBadItems | Array | QC不良项目列表 |

### QC不良项目字段
| 字段名 | 类型 | 描述 |
|--------|------|------|
| mo | String | 制造订单号 |
| plId | String | 线体ID |
| lbId | String | 标签ID |
| badItemId | String | 不良项目ID |
| badPoint | String | 不良点位 |
| fieldEx2 | String | 扩展字段2 |

## 业务逻辑说明

### 查询流程
1. **第一步**: 根据线体ID和日期范围查询失败的标签工艺过程分析数据（`MIN(IS_PASS) = 'N'`的标签）
2. **第二步**: 提取第一步结果中的所有LB_ID
3. **第三步**: 根据LB_ID列表查询对应的QC不良项目信息
4. **第四步**: 将QC不良项目数据按LB_ID分组，并与工艺过程分析数据组合

### 数据关联
- 通过 `lbId` 字段关联标签工艺过程分析数据和QC不良项目数据
- 如果某个标签没有对应的QC不良项目记录，`qcBadItems` 字段将为空数组
- 一个标签可能对应多个QC不良项目记录

## 实现说明
1. **实体类**: 
   - `LabelWorkProcessWithQcBadItems.java` - 组合实体类
   - `LabelWorkProcessAnalysis.java` - 工艺过程分析实体
   - `QcBadItem.java` - QC不良项目实体
2. **Service**: `OeeService.getLabelWorkProcessAnalysisWithQcBadItems()` - 组合业务逻辑
3. **Controller**: `OeeController.getLabelWorkProcessAnalysisWithQcBadItems()` - REST接口层

## 错误处理
- 日期格式错误会返回400 Bad Request
- 数据库连接错误会返回500 Internal Server Error
- 参数缺失会返回400 Bad Request
- 如果没有找到失败的标签，返回空数组

## 测试命令
```bash
curl -X GET "http://localhost:9996/oee-mes-server/oee/label-work-process-analysis-with-qc-bad-items?plId=SMT1-1&startDate=2025-07-08%2008:00:00&endDate=2025-07-09%2008:00:00"
```

## 使用场景
- **综合质量分析**: 同时查看工艺过程失败情况和具体的不良项目
- **根因分析**: 通过工艺过程数据和不良项目数据进行根因分析
- **质量报表**: 生成包含工艺过程和质量检测信息的综合报表
- **问题追踪**: 追踪特定时间段内的质量问题及其详细信息

## 性能优化
- 使用批量查询减少数据库访问次数
- 通过Map进行数据分组，提高数据关联效率
- 只查询失败的标签，减少不必要的数据传输

## 注意事项
1. 该接口只返回工艺过程中至少有一个工序失败的标签
2. QC不良项目数据可能为空，表示该标签虽然工艺过程失败但没有QC记录
3. 返回结果按最终工序完成时间排序
4. 日期参数格式必须为 "yyyy-MM-dd HH:mm:ss"
