package com.hongjing.oee_mes.domain;

import java.util.Date;
import java.util.List;

/**
 * 标签工艺过程分析与QC不良项目组合实体类 包含标签工艺过程分析数据和对应的QC不良项目列表
 */
public class LabelWorkProcessWithQcBadItems {

	private String lbId;

	private String lbGrp;

	private String plId;

	private String mo;

	private String keyWpFirstResult;

	private String finalResult;

	private Date finalWpCmpDate;

	private List<QcBadItem> qcBadItems;

	public LabelWorkProcessWithQcBadItems() {
	}

	public LabelWorkProcessWithQcBadItems(String lbId, String lbGrp, String plId, String mo, String keyWpFirstResult,
			String finalResult, Date finalWpCmpDate, List<QcBadItem> qcBadItems) {
		this.lbId = lbId;
		this.lbGrp = lbGrp;
		this.plId = plId;
		this.mo = mo;
		this.keyWpFirstResult = keyWpFirstResult;
		this.finalResult = finalResult;
		this.finalWpCmpDate = finalWpCmpDate;
		this.qcBadItems = qcBadItems;
	}

	/**
	 * 从LabelWorkProcessAnalysis创建实例
	 */
	public static LabelWorkProcessWithQcBadItems fromLabelWorkProcessAnalysis(LabelWorkProcessAnalysis analysis) {
		return new LabelWorkProcessWithQcBadItems(analysis.getLbId(), analysis.getLbGrp(), analysis.getPlId(),
				analysis.getMo(), analysis.getKeyWpFirstResult(), analysis.getFinalResult(),
				analysis.getFinalWpCmpDate(), null // QC不良项目列表稍后设置
		);
	}

	public String getLbId() {
		return lbId;
	}

	public void setLbId(String lbId) {
		this.lbId = lbId;
	}

	public String getLbGrp() {
		return lbGrp;
	}

	public void setLbGrp(String lbGrp) {
		this.lbGrp = lbGrp;
	}

	public String getPlId() {
		return plId;
	}

	public void setPlId(String plId) {
		this.plId = plId;
	}

	public String getMo() {
		return mo;
	}

	public void setMo(String mo) {
		this.mo = mo;
	}

	public String getKeyWpFirstResult() {
		return keyWpFirstResult;
	}

	public void setKeyWpFirstResult(String keyWpFirstResult) {
		this.keyWpFirstResult = keyWpFirstResult;
	}

	public String getFinalResult() {
		return finalResult;
	}

	public void setFinalResult(String finalResult) {
		this.finalResult = finalResult;
	}

	public Date getFinalWpCmpDate() {
		return finalWpCmpDate;
	}

	public void setFinalWpCmpDate(Date finalWpCmpDate) {
		this.finalWpCmpDate = finalWpCmpDate;
	}

	public List<QcBadItem> getQcBadItems() {
		return qcBadItems;
	}

	public void setQcBadItems(List<QcBadItem> qcBadItems) {
		this.qcBadItems = qcBadItems;
	}

	@Override
	public String toString() {
		return "LabelWorkProcessWithQcBadItems{" + "lbId='" + lbId + '\'' + ", lbGrp='" + lbGrp + '\'' + ", plId='"
				+ plId + '\'' + ", mo='" + mo + '\'' + ", keyWpFirstResult='" + keyWpFirstResult + '\''
				+ ", finalResult='" + finalResult + '\'' + ", finalWpCmpDate=" + finalWpCmpDate + ", qcBadItems="
				+ qcBadItems + '}';
	}

}
