package com.hongjing.oee_mes.domain;

import java.util.Date;
import java.util.List;

/**
 * 标签工艺过程分析与QC不良项目组合实体类 包含标签工艺过程分析数据和对应的QC不良项目列表
 */
public class LabelWorkProcessWithQcBadItems {

	private String lbId;

	private String plId;

	private String mo;

	private String firstResult;

	private String keyWpFirstResult;

	private String finalResult;

	private Date finalWpCmpDate;

	private List<QcBadItem> qcBadItems;

	public LabelWorkProcessWithQcBadItems() {
	}

	public LabelWorkProcessWithQcBadItems(String lbId, String plId, String mo, String firstResult,
			String keyWpFirstResult, String finalResult, Date finalWpCmpDate, List<QcBadItem> qcBadItems) {
		this.lbId = lbId;
		this.plId = plId;
		this.mo = mo;
		this.firstResult = firstResult;
		this.keyWpFirstResult = keyWpFirstResult;
		this.finalResult = finalResult;
		this.finalWpCmpDate = finalWpCmpDate;
		this.qcBadItems = qcBadItems;
	}

	/**
	 * 从LabelWorkProcessAnalysis创建实例
	 */
	public static LabelWorkProcessWithQcBadItems fromLabelWorkProcessAnalysis(LabelWorkProcessAnalysis analysis) {
		return new LabelWorkProcessWithQcBadItems(analysis.getLbId(), analysis.getPlId(), analysis.getMo(),
				analysis.getFirstResult(), analysis.getKeyWpFirstResult(), analysis.getFinalResult(),
				analysis.getFinalWpCmpDate(), null // QC不良项目列表稍后设置
		);
	}

	public String getLbId() {
		return lbId;
	}

	public void setLbId(String lbId) {
		this.lbId = lbId;
	}

	public String getPlId() {
		return plId;
	}

	public void setPlId(String plId) {
		this.plId = plId;
	}

	public String getMo() {
		return mo;
	}

	public void setMo(String mo) {
		this.mo = mo;
	}

	public String getFirstResult() {
		return firstResult;
	}

	public void setFirstResult(String firstResult) {
		this.firstResult = firstResult;
	}

	public String getKeyWpFirstResult() {
		return keyWpFirstResult;
	}

	public void setKeyWpFirstResult(String keyWpFirstResult) {
		this.keyWpFirstResult = keyWpFirstResult;
	}

	public String getFinalResult() {
		return finalResult;
	}

	public void setFinalResult(String finalResult) {
		this.finalResult = finalResult;
	}

	public Date getFinalWpCmpDate() {
		return finalWpCmpDate;
	}

	public void setFinalWpCmpDate(Date finalWpCmpDate) {
		this.finalWpCmpDate = finalWpCmpDate;
	}

	public List<QcBadItem> getQcBadItems() {
		return qcBadItems;
	}

	public void setQcBadItems(List<QcBadItem> qcBadItems) {
		this.qcBadItems = qcBadItems;
	}

	@Override
	public String toString() {
		return "LabelWorkProcessWithQcBadItems{" + "lbId='" + lbId + '\'' + ", plId='" + plId + '\'' + ", mo='" + mo
				+ '\'' + ", firstResult='" + firstResult + '\'' + ", keyWpFirstResult='" + keyWpFirstResult + '\''
				+ ", finalResult='" + finalResult + '\'' + ", finalWpCmpDate=" + finalWpCmpDate + ", qcBadItems="
				+ qcBadItems + '}';
	}

}
