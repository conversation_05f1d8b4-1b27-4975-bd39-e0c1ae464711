package com.hongjing.oee_mes.controller;

import com.hongjing.oee_mes.domain.LabelWorkProcessWithQcBadItems;
import com.hongjing.oee_mes.domain.QcBadItem;
import com.hongjing.oee_mes.service.OeeService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(OeeController.class)
public class LabelWorkProcessWithQcBadItemsControllerTest {

	@Autowired
	private MockMvc mockMvc;

	@MockBean
	private OeeService oeeService;

	@Test
	public void testGetLabelWorkProcessAnalysisWithQcBadItems() throws Exception {
		// 准备测试数据
		Date testDate = new Date();

		// 创建QC不良项目数据
		List<QcBadItem> qcBadItems1 = Arrays.asList(
				new QcBadItem("MO202507090001", "SMT1-1", "38364648-001 51897217", "SOLDER_BRIDGE", "U1", "严重"),
				new QcBadItem("MO202507090001", "SMT1-1", "38364648-001 51897217", "MISSING_COMPONENT", "R5", "一般"));

		List<QcBadItem> qcBadItems2 = Arrays.asList(); // 空的QC不良项目列表

		// 创建组合数据
		List<LabelWorkProcessWithQcBadItems> mockResponse = Arrays.asList(
				new LabelWorkProcessWithQcBadItems("38364648-001 51897217", "SMT1-1", "MO202507090001", "N", "N", "N",
						testDate, qcBadItems1),
				new LabelWorkProcessWithQcBadItems("38364648-001 51897218", "SMT1-1", "MO202507090001", "N", "Y", "N",
						testDate, qcBadItems2));

		// 模拟服务层返回
		when(oeeService.getLabelWorkProcessAnalysisWithQcBadItems(anyString(), any(Date.class), any(Date.class)))
			.thenReturn(mockResponse);

		// 执行测试
		mockMvc
			.perform(get("/oee/label-work-process-analysis-with-qc-bad-items").param("plId", "SMT1-1")
				.param("startDate", "2025-07-08 08:00:00")
				.param("endDate", "2025-07-09 08:00:00"))
			.andExpect(status().isOk())
			.andExpect(content().contentType(MediaType.APPLICATION_JSON))
			.andExpect(jsonPath("$").isArray())
			.andExpect(jsonPath("$.length()").value(2))
			// 验证第一个标签的数据
			.andExpect(jsonPath("$[0].lbId").value("38364648-001 51897217"))
			.andExpect(jsonPath("$[0].plId").value("SMT1-1"))
			.andExpect(jsonPath("$[0].mo").value("MO202507090001"))
			.andExpect(jsonPath("$[0].firstResult").value("N"))
			.andExpect(jsonPath("$[0].keyWpFirstResult").value("N"))
			.andExpect(jsonPath("$[0].finalResult").value("N"))
			.andExpect(jsonPath("$[0].qcBadItems").isArray())
			.andExpect(jsonPath("$[0].qcBadItems.length()").value(2))
			.andExpect(jsonPath("$[0].qcBadItems[0].badItemId").value("SOLDER_BRIDGE"))
			.andExpect(jsonPath("$[0].qcBadItems[0].badPoint").value("U1"))
			.andExpect(jsonPath("$[0].qcBadItems[1].badItemId").value("MISSING_COMPONENT"))
			.andExpect(jsonPath("$[0].qcBadItems[1].badPoint").value("R5"))
			// 验证第二个标签的数据
			.andExpect(jsonPath("$[1].lbId").value("38364648-001 51897218"))
			.andExpect(jsonPath("$[1].plId").value("SMT1-1"))
			.andExpect(jsonPath("$[1].mo").value("MO202507090001"))
			.andExpect(jsonPath("$[1].firstResult").value("N"))
			.andExpect(jsonPath("$[1].keyWpFirstResult").value("Y"))
			.andExpect(jsonPath("$[1].finalResult").value("N"))
			.andExpect(jsonPath("$[1].qcBadItems").isArray())
			.andExpect(jsonPath("$[1].qcBadItems.length()").value(0));
	}

	@Test
	public void testGetLabelWorkProcessAnalysisWithQcBadItems_EmptyResult() throws Exception {
		// 模拟服务层返回空列表
		when(oeeService.getLabelWorkProcessAnalysisWithQcBadItems(anyString(), any(Date.class), any(Date.class)))
			.thenReturn(Arrays.asList());

		// 执行测试
		mockMvc
			.perform(get("/oee/label-work-process-analysis-with-qc-bad-items").param("plId", "SMT1-1")
				.param("startDate", "2025-07-08 08:00:00")
				.param("endDate", "2025-07-09 08:00:00"))
			.andExpect(status().isOk())
			.andExpect(content().contentType(MediaType.APPLICATION_JSON))
			.andExpect(jsonPath("$").isArray())
			.andExpect(jsonPath("$.length()").value(0));
	}

	@Test
	public void testGetLabelWorkProcessAnalysisWithQcBadItems_InvalidDateFormat() throws Exception {
		// 测试无效的日期格式
		mockMvc
			.perform(get("/oee/label-work-process-analysis-with-qc-bad-items").param("plId", "SMT1-1")
				.param("startDate", "invalid-date")
				.param("endDate", "2025-07-09 08:00:00"))
			.andExpect(status().isBadRequest());
	}

}
