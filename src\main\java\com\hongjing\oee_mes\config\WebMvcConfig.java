package com.hongjing.oee_mes.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import com.hongjing.oee_mes.interceptor.RateLimitInterceptor;

@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

	@Autowired
	private RateLimitInterceptor rateLimitInterceptor;

	@Override
	public void addInterceptors(InterceptorRegistry registry) {
		// 注册拦截器，并指定它只拦截 /label-work-process/latest-status 这个路径
		registry.addInterceptor(rateLimitInterceptor)
			.addPathPatterns("/label-work-process/latest-status")
			.addPathPatterns("/sample/ids-by-lbids");
	}

}