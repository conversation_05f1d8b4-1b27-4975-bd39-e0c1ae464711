package com.hongjing.oee_mes.controller;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.hongjing.oee_mes.domain.LabelWorkProcessAnalysis;
import com.hongjing.oee_mes.domain.LabelWorkProcessWithQcBadItems;
import com.hongjing.oee_mes.domain.Mo;
import com.hongjing.oee_mes.domain.QcBadItem;
import com.hongjing.oee_mes.domain.StandardCapacity;
import com.hongjing.oee_mes.service.OeeService;

@RestController
@RequestMapping("/oee")
public class OeeController {

	private final OeeService oeeService;

	public OeeController(OeeService oeeService) {
		this.oeeService = oeeService;
	}

	@GetMapping("/standardCapacity")
	public StandardCapacity getStandardCapacity(@RequestParam("prodId") String prodId,
			@RequestParam("plId") String plId, @RequestParam("bomSide") String bomSide) {
		return oeeService.getStandardCapacity(prodId, plId, bomSide);
	}

	@GetMapping("/mo")
	public List<Mo> getMoByPlIdAndDate(@RequestParam("plId") String plId,
			@RequestParam("startDate") String startDateStr, @RequestParam("endDate") String endDateStr)
			throws ParseException {
		SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		Date startDate = dateFormat.parse(startDateStr);
		Date endDate = dateFormat.parse(endDateStr);
		return oeeService.getMoByPlIdAndDate(plId, startDate, endDate);
	}

	@GetMapping("/label-work-process-analysis")
	public List<LabelWorkProcessAnalysis> getLabelWorkProcessAnalysis(@RequestParam("plId") String plId,
			@RequestParam("startDate") String startDateStr, @RequestParam("endDate") String endDateStr)
			throws ParseException {
		SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		Date startDate = dateFormat.parse(startDateStr);
		Date endDate = dateFormat.parse(endDateStr);
		return oeeService.getLabelWorkProcessAnalysis(plId, startDate, endDate);
	}

	@PostMapping("/qc-bad-items")
	public List<QcBadItem> getQcBadItemsByLbIds(@RequestBody List<String> lbIds) {
		return oeeService.getQcBadItemsByLbIds(lbIds);
	}

	@GetMapping("/label-work-process-analysis-with-qc-bad-items")
	public List<LabelWorkProcessWithQcBadItems> getLabelWorkProcessAnalysisWithQcBadItems(
			@RequestParam("plId") String plId, @RequestParam("startDate") String startDateStr,
			@RequestParam("endDate") String endDateStr) throws ParseException {
		SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		Date startDate = dateFormat.parse(startDateStr);
		Date endDate = dateFormat.parse(endDateStr);
		return oeeService.getLabelWorkProcessAnalysisWithQcBadItems(plId, startDate, endDate);
	}

}
