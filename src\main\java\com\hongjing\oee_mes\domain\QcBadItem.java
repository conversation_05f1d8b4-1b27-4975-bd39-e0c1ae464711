package com.hongjing.oee_mes.domain;

/**
 * QC不良项目查询结果实体类
 * 对应TB_PM_QC_HD和TB_PM_QC_DT表的联合查询结果
 */
public class QcBadItem {

    private String mo;
    private String plId;
    private String lbId;
    private String badItemId;
    private String badPoint;
    private String fieldEx2;

    public QcBadItem() {
    }

    public QcBadItem(String mo, String plId, String lbId, String badItemId, String badPoint, String fieldEx2) {
        this.mo = mo;
        this.plId = plId;
        this.lbId = lbId;
        this.badItemId = badItemId;
        this.badPoint = badPoint;
        this.fieldEx2 = fieldEx2;
    }

    public String getMo() {
        return mo;
    }

    public void setMo(String mo) {
        this.mo = mo;
    }

    public String getPlId() {
        return plId;
    }

    public void setPlId(String plId) {
        this.plId = plId;
    }

    public String getLbId() {
        return lbId;
    }

    public void setLbId(String lbId) {
        this.lbId = lbId;
    }

    public String getBadItemId() {
        return badItemId;
    }

    public void setBadItemId(String badItemId) {
        this.badItemId = badItemId;
    }

    public String getBadPoint() {
        return badPoint;
    }

    public void setBadPoint(String badPoint) {
        this.badPoint = badPoint;
    }

    public String getFieldEx2() {
        return fieldEx2;
    }

    public void setFieldEx2(String fieldEx2) {
        this.fieldEx2 = fieldEx2;
    }

    @Override
    public String toString() {
        return "QcBadItem{" +
                "mo='" + mo + '\'' +
                ", plId='" + plId + '\'' +
                ", lbId='" + lbId + '\'' +
                ", badItemId='" + badItemId + '\'' +
                ", badPoint='" + badPoint + '\'' +
                ", fieldEx2='" + fieldEx2 + '\'' +
                '}';
    }
}
