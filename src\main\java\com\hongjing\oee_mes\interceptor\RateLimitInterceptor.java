package com.hongjing.oee_mes.interceptor;

import java.time.Duration;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import io.github.bucket4j.Bucket;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

@Component
public class RateLimitInterceptor implements HandlerInterceptor {

	// 使用 ConcurrentHashMap 来为不同的API端点存储独立的令牌桶
	// Key: 请求的URI (例如, "/label-work-process/latest-status")
	// Value: 对应的 Bucket
	private final Map<String, Bucket> buckets = new ConcurrentHashMap<>();

	// 为 "1秒1次" 创建一个令牌桶
	private Bucket createNewBucket() {
		// 控制瞬时突发流量：防止在极短时间内（如1秒内）收到大量请求。
		// 控制长期平均流量：防止在较长时间内（如1分钟内）总的请求量过大。
		Bucket bucket = Bucket.builder()
			.addLimit(limit -> limit.capacity(1).refillGreedy(1, Duration.ofSeconds(1)))
			.addLimit(limit -> limit.capacity(10).refillGreedy(10, Duration.ofMinutes(1)))
			.build();
		return bucket;
	}

	@Override
	public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
			throws Exception {
		String uri = request.getRequestURI();

		// 如果该URI的令牌桶不存在，则创建一个新的并放入map中
		// computeIfAbsent 是线程安全的，确保只创建一个Bucket实例
		Bucket bucket = buckets.computeIfAbsent(uri, k -> createNewBucket());

		// 尝试从桶中消费一个令牌
		if (bucket.tryConsume(1)) {
			// 令牌充足，请求继续
			return true;
		}
		else {
			// 令牌不足，请求被拒绝
			response.setStatus(HttpStatus.TOO_MANY_REQUESTS.value()); // HTTP 429
			response.setContentType("application/json;charset=UTF-8");
			response.getWriter().write("{\"message\": \"请求过于频繁，请稍后再试\"}");
			return false; // 阻止请求继续向下执行
		}
	}

}