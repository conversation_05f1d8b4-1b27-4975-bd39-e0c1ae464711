# 标签工艺过程分析接口文档

## 接口描述
根据线体ID和日期范围查询TB_PM_MO_LBWP表中失败标签的工艺过程分析数据。该接口专门用于分析生产过程中出现问题的标签，提供首次结果、关键工序结果、最终结果等统计信息。

## 接口地址
```
GET /oee/label-work-process-analysis
```

## 请求参数
| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| plId | String | 是 | 线体ID | SMT1-1 |
| startDate | String | 是 | 开始日期时间 | 2025-07-08 08:00:00 |
| endDate | String | 是 | 结束日期时间 | 2025-07-09 08:00:00 |

## 请求示例
```
GET /oee/label-work-process-analysis?plId=SMT1-1&startDate=2025-07-08 08:00:00&endDate=2025-07-09 08:00:00
```

## 响应格式
```json
[
  {
    "lbId": "LB001",
    "plId": "SMT1-1", 
    "mo": "BOT-VNNT25070701-S_S1-S",
    "firstResult": "N",
    "keyWpFirstResult": "N",
    "finalResult": "N",
    "finalWpCmpDate": "2025-07-08T10:30:00.000+00:00"
  },
  {
    "lbId": "LB002",
    "plId": "SMT1-1",
    "mo": "BOT-VNNT25070701-S_S1-S", 
    "firstResult": "N",
    "keyWpFirstResult": "Y",
    "finalResult": "N",
    "finalWpCmpDate": "2025-07-08T11:15:00.000+00:00"
  }
]
```

## 响应字段说明
| 字段名 | 类型 | 描述 |
|--------|------|------|
| lbId | String | 标签ID |
| plId | String | 线体ID |
| mo | String | 制造订单号 |
| firstResult | String | 首次结果（所有工序的最小IS_PASS值） |
| keyWpFirstResult | String | 关键工序首次结果（仅考虑SMT-03和SMT-06工序的最小IS_PASS值） |
| finalResult | String | 最终结果（按完成时间排序的最后一个IS_PASS值） |
| finalWpCmpDate | Date | 最终工序完成时间 |

## 业务逻辑说明

### 查询条件
- 只返回 `MIN(IS_PASS) = 'N'` 的标签（即至少有一个工序失败的标签）
- 按最终工序完成时间排序

### 字段计算逻辑

1. **firstResult**: 使用 `MIN(IS_PASS)` 计算，表示该标签在所有工序中是否有失败
2. **keyWpFirstResult**: 使用条件MIN计算
   ```sql
   MIN(
     CASE
       WHEN WP_ID IN ('SMT-03', 'SMT-06') THEN IS_PASS
       ELSE 'Y'
     END
   )
   ```
   - 只考虑关键工序（SMT-03, SMT-06）的IS_PASS值
   - 非关键工序被视为'Y'，不影响最终结果
3. **finalResult**: 使用 `MAX(IS_PASS) KEEP (DENSE_RANK LAST ORDER BY WP_CMP_DATE)` 计算最后完成工序的结果
4. **finalWpCmpDate**: 使用 `MAX(WP_CMP_DATE) KEEP (DENSE_RANK LAST ORDER BY WP_CMP_DATE)` 获取最终完成时间

## 原始SQL查询
```sql
SELECT
  LB_ID,
  PL_ID,
  MO,
  MIN(IS_PASS) AS first_result, 
  MIN(
    CASE
      WHEN WP_ID IN ('SMT-03', 'SMT-06') THEN IS_PASS
      ELSE 'Y'
    END
  ) AS KEY_WP_FIRST_RESULT,
  MAX(IS_PASS) KEEP (DENSE_RANK LAST ORDER BY WP_CMP_DATE) AS final_result, 
  MAX(WP_CMP_DATE) KEEP (DENSE_RANK LAST ORDER BY WP_CMP_DATE) AS FINAL_WP_CMP_DATE
FROM
  TB_PM_MO_LBWP
WHERE 
  WP_CMP_DATE >= TO_DATE('2025-07-08 08:00:00', 'YYYY-MM-DD HH24:MI:SS')
  AND WP_CMP_DATE < TO_DATE('2025-07-08 08:00:00', 'YYYY-MM-DD HH24:MI:SS') + 1
  AND PL_ID = 'SMT1-1'
GROUP BY
  LB_ID, PL_ID, MO
HAVING 
  MIN(IS_PASS) = 'N'
ORDER BY
  FINAL_WP_CMP_DATE;
```

## 实现说明
1. **实体类**: `LabelWorkProcessAnalysis.java` - 对应查询结果的数据结构
2. **Repository**: `OeeRepository.getLabelWorkProcessAnalysis()` - 执行复杂的数据库查询
3. **Service**: `OeeService.getLabelWorkProcessAnalysis()` - 业务逻辑层
4. **Controller**: `OeeController.getLabelWorkProcessAnalysis()` - REST接口层

## 错误处理
- 日期格式错误会返回400 Bad Request
- 数据库连接错误会返回500 Internal Server Error  
- 参数缺失会返回400 Bad Request

## 测试命令
```bash
curl -X GET "http://localhost:9996/oee-mes-server/oee/label-work-process-analysis?plId=SMT1-1&startDate=2025-07-08%2008:00:00&endDate=2025-07-09%2008:00:00"
```

## 使用场景
- 质量分析：识别生产过程中的问题标签
- 工序分析：区分关键工序和普通工序的失败情况
- 时间分析：按完成时间排序，便于时序分析
- 故障排查：快速定位失败的标签和相关订单
