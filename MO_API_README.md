# OEE系统查询接口文档

本文档包含两个主要的查询接口：MO查询接口和标签工艺过程分析接口。

## 1. MO查询接口

### 接口描述
根据线体ID和日期范围查询TB_PP_MO表中的MO信息。

### 接口地址
```
GET /oee/mo
```

## 请求参数
| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| plId | String | 是 | 线体ID | SMT1-1 |
| startDate | String | 是 | 开始日期时间 | 2025-07-08 08:00:00 |
| endDate | String | 是 | 结束日期时间 | 2025-07-09 08:00:00 |

## 请求示例
```
GET /oee/mo?plId=SMT1-1&startDate=2025-07-08 08:00:00&endDate=2025-07-09 08:00:00
```

## 响应格式
```json
[
  {
    "mo": "MO001",
    "plId": "SMT1-1",
    "tbdate": "2025-07-08T08:30:00.000+00:00"
  },
  {
    "mo": "MO002", 
    "plId": "SMT1-1",
    "tbdate": "2025-07-08T09:15:00.000+00:00"
  }
]
```

## 响应字段说明
| 字段名 | 类型 | 描述 |
|--------|------|------|
| mo | String | 制造订单号 |
| plId | String | 线体ID |
| tbdate | Date | 订单日期时间 |

## SQL查询语句
原始查询需求：
```sql
select MO,PL_ID,T_BDATE from TB_PP_MO 
where PL_ID = 'SMT1-1' 
and T_BDATE >= TO_DATE('2025-07-08 08:00:00', 'YYYY-MM-DD HH24:MI:SS')
AND T_BDATE < TO_DATE('2025-07-08 08:00:00', 'YYYY-MM-DD HH24:MI:SS') + 1;
```

注意：原始查询中的日期条件有问题（开始和结束时间相同），接口实现中已修正为使用不同的开始和结束时间。

## 实现说明
1. **实体类**: `Mo.java` - 对应TB_PP_MO表的查询结果
2. **Repository**: `OeeRepository.getMoByPlIdAndDate()` - 执行数据库查询
3. **Service**: `OeeService.getMoByPlIdAndDate()` - 业务逻辑层
4. **Controller**: `OeeController.getMoByPlIdAndDate()` - REST接口层

## 错误处理
- 日期格式错误会返回400 Bad Request
- 数据库连接错误会返回500 Internal Server Error
- 参数缺失会返回400 Bad Request

### 测试
可以使用以下curl命令测试接口：
```bash
curl -X GET "http://localhost:9996/oee-mes-server/oee/mo?plId=SMT1-1&startDate=2025-07-08%2008:00:00&endDate=2025-07-09%2008:00:00"
```

---

## 2. 标签工艺过程分析接口

### 接口描述
根据线体ID和日期范围查询TB_PM_MO_LBWP表中失败标签的工艺过程分析数据。该接口专门用于分析生产过程中出现问题的标签。

### 接口地址
```
GET /oee/label-work-process-analysis
```

### 请求参数
| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| plId | String | 是 | 线体ID | SMT1-1 |
| startDate | String | 是 | 开始日期时间 | 2025-07-08 08:00:00 |
| endDate | String | 是 | 结束日期时间 | 2025-07-09 08:00:00 |

### 请求示例
```
GET /oee/label-work-process-analysis?plId=SMT1-1&startDate=2025-07-08 08:00:00&endDate=2025-07-09 08:00:00
```

### 响应格式
```json
[
  {
    "lbId": "LB001",
    "plId": "SMT1-1",
    "mo": "BOT-VNNT25070701-S_S1-S",
    "firstResult": "N",
    "keyWpFirstResult": "N",
    "finalResult": "N",
    "finalWpCmpDate": "2025-07-08T10:30:00.000+00:00"
  }
]
```

### 响应字段说明
| 字段名 | 类型 | 描述 |
|--------|------|------|
| lbId | String | 标签ID |
| plId | String | 线体ID |
| mo | String | 制造订单号 |
| firstResult | String | 首次结果（所有工序的最小IS_PASS值） |
| keyWpFirstResult | String | 关键工序首次结果（仅考虑SMT-03和SMT-06工序） |
| finalResult | String | 最终结果（按完成时间排序的最后一个IS_PASS值） |
| finalWpCmpDate | Date | 最终工序完成时间 |

### 业务逻辑
- 只返回至少有一个工序失败的标签（MIN(IS_PASS) = 'N'）
- 关键工序（SMT-03, SMT-06）单独统计
- 按最终工序完成时间排序

### 测试
```bash
curl -X GET "http://localhost:9996/oee-mes-server/oee/label-work-process-analysis?plId=SMT1-1&startDate=2025-07-08%2008:00:00&endDate=2025-07-09%2008:00:00"
```

---

## 总体架构说明

两个接口都遵循相同的分层架构：
1. **Controller层**: 处理HTTP请求和响应
2. **Service层**: 业务逻辑处理
3. **Repository层**: 数据访问层
4. **Domain层**: 实体类定义

## 公共错误处理
- 日期格式错误: 400 Bad Request
- 数据库连接错误: 500 Internal Server Error
- 参数缺失: 400 Bad Request
