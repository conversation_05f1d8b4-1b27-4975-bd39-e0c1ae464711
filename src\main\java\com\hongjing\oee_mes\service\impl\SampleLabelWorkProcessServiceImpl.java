package com.hongjing.oee_mes.service.impl;

import java.util.List;

import org.springframework.stereotype.Service;

import com.hongjing.oee_mes.repository.SampleLabelWorkProcessRepository;
import com.hongjing.oee_mes.service.SampleLabelWorkProcessService;

@Service
public class SampleLabelWorkProcessServiceImpl implements SampleLabelWorkProcessService {

	private final SampleLabelWorkProcessRepository repository;

	public SampleLabelWorkProcessServiceImpl(SampleLabelWorkProcessRepository repository) {
		this.repository = repository;
	}

	@Override
	public List<String> getIdsByLbIds(List<String> lbIds) {
		return repository.getIdsByLbIds(lbIds);
	}

}
