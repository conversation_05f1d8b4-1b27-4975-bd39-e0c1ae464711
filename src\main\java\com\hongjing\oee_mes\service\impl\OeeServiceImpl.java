package com.hongjing.oee_mes.service.impl;

import java.util.Date;
import java.util.List;

import org.springframework.stereotype.Service;

import com.hongjing.oee_mes.domain.Device;
import com.hongjing.oee_mes.domain.LabelWorkProcessAnalysis;
import com.hongjing.oee_mes.domain.Line;
import com.hongjing.oee_mes.domain.Mo;
import com.hongjing.oee_mes.domain.QcBadItem;
import com.hongjing.oee_mes.domain.StandardCapacity;
import com.hongjing.oee_mes.repository.OeeRepository;
import com.hongjing.oee_mes.service.OeeService;

@Service
public class OeeServiceImpl implements OeeService {

	private final OeeRepository repository;

	public OeeServiceImpl(OeeRepository repository) {
		this.repository = repository;
	}

	@Override
	public List<Line> getLineList() {
		// TODO Auto-generated method stub
		throw new UnsupportedOperationException("Unimplemented method 'getLineList'");
	}

	@Override
	public List<Device> getDeviceListByLine(String code) {
		// TODO Auto-generated method stub
		throw new UnsupportedOperationException("Unimplemented method 'getDeviceListByLine'");
	}

	@Override
	public StandardCapacity getStandardCapacity(String prodId, String plId, String bomSide) {
		List<StandardCapacity> standardCapacityList = repository.getStandardCapacity(prodId, plId, bomSide);

		if (standardCapacityList == null || standardCapacityList.isEmpty()) {
			// 返回一个空的 StandardCapacity 对象
			return new StandardCapacity(); // 或者使用具体的默认值
		}

		return standardCapacityList.get(0);
	}

	@Override
	public List<Mo> getMoByPlIdAndDate(String plId, Date startDate, Date endDate) {
		return repository.getMoByPlIdAndDate(plId, startDate, endDate);
	}

	@Override
	public List<LabelWorkProcessAnalysis> getLabelWorkProcessAnalysis(String plId, Date startDate, Date endDate) {
		return repository.getLabelWorkProcessAnalysis(plId, startDate, endDate);
	}

	@Override
	public List<QcBadItem> getQcBadItemsByLbIds(List<String> lbIds) {
		return repository.getQcBadItemsByLbIds(lbIds);
	}

}
