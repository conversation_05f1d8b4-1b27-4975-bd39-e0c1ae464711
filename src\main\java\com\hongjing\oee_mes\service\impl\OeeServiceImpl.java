package com.hongjing.oee_mes.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;

import com.hongjing.oee_mes.domain.Device;
import com.hongjing.oee_mes.domain.LabelWorkProcessAnalysis;
import com.hongjing.oee_mes.domain.LabelWorkProcessWithQcBadItems;
import com.hongjing.oee_mes.domain.Line;
import com.hongjing.oee_mes.domain.Mo;
import com.hongjing.oee_mes.domain.QcBadItem;
import com.hongjing.oee_mes.domain.StandardCapacity;
import com.hongjing.oee_mes.repository.OeeRepository;
import com.hongjing.oee_mes.service.OeeService;

@Service
public class OeeServiceImpl implements OeeService {

	private final OeeRepository repository;

	public OeeServiceImpl(OeeRepository repository) {
		this.repository = repository;
	}

	@Override
	public List<Line> getLineList() {
		// TODO Auto-generated method stub
		throw new UnsupportedOperationException("Unimplemented method 'getLineList'");
	}

	@Override
	public List<Device> getDeviceListByLine(String code) {
		// TODO Auto-generated method stub
		throw new UnsupportedOperationException("Unimplemented method 'getDeviceListByLine'");
	}

	@Override
	public StandardCapacity getStandardCapacity(String prodId, String plId, String bomSide) {
		List<StandardCapacity> standardCapacityList = repository.getStandardCapacity(prodId, plId, bomSide);

		if (standardCapacityList == null || standardCapacityList.isEmpty()) {
			// 返回一个空的 StandardCapacity 对象
			return new StandardCapacity(); // 或者使用具体的默认值
		}

		return standardCapacityList.get(0);
	}

	@Override
	public List<Mo> getMoByPlIdAndDate(String plId, Date startDate, Date endDate) {
		return repository.getMoByPlIdAndDate(plId, startDate, endDate);
	}

	@Override
	public List<LabelWorkProcessAnalysis> getLabelWorkProcessAnalysis(String plId, Date startDate, Date endDate) {
		return repository.getLabelWorkProcessAnalysis(plId, startDate, endDate);
	}

	@Override
	public List<QcBadItem> getQcBadItemsByLbIds(List<String> lbIds) {
		return repository.getQcBadItemsByLbIds(lbIds);
	}

	@Override
	public List<LabelWorkProcessWithQcBadItems> getLabelWorkProcessAnalysisWithQcBadItems(String plId, Date startDate,
			Date endDate) {
		// 1. 先获取标签工艺过程分析数据
		List<LabelWorkProcessAnalysis> labelAnalysisList = repository.getLabelWorkProcessAnalysis(plId, startDate,
				endDate);

		if (labelAnalysisList.isEmpty()) {
			return List.of(); // 如果没有分析数据，直接返回空列表
		}

		// 2. 提取所有的LB_ID
		List<String> lbIds = labelAnalysisList.stream()
			.map(LabelWorkProcessAnalysis::getLbId)
			.collect(Collectors.toList());

		// 3. 根据LB_ID列表查询QC不良项目数据
		List<QcBadItem> qcBadItems = repository.getQcBadItemsByLbIds(lbIds);

		// 4. 按LB_ID分组QC不良项目数据
		Map<String, List<QcBadItem>> qcBadItemsMap = qcBadItems.stream()
			.collect(Collectors.groupingBy(QcBadItem::getLbId));

		// 5. 组合数据
		return labelAnalysisList.stream().map(analysis -> {
			LabelWorkProcessWithQcBadItems combined = LabelWorkProcessWithQcBadItems
				.fromLabelWorkProcessAnalysis(analysis);
			// 设置对应的QC不良项目列表，如果没有则设置为空列表
			combined.setQcBadItems(qcBadItemsMap.getOrDefault(analysis.getLbId(), List.of()));
			return combined;
		}).collect(Collectors.toList());
	}

}
