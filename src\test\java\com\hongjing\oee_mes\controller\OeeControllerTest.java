package com.hongjing.oee_mes.controller;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.web.servlet.MockMvc;

import com.hongjing.oee_mes.domain.LabelWorkProcessAnalysis;
import com.hongjing.oee_mes.domain.Mo;
import com.hongjing.oee_mes.service.OeeService;

@WebMvcTest(OeeController.class)
public class OeeControllerTest {

	@Autowired
	private MockMvc mockMvc;

	@MockBean
	private OeeService oeeService;

	@Test
	public void testGetMoByPlIdAndDate() throws Exception {
		// 准备测试数据
		Date testDate = new Date();
		List<Mo> mockMoList = Arrays.asList(new Mo("MO001", "SMT1-1", testDate), new Mo("MO002", "SMT1-1", testDate));

		// 模拟服务层返回
		when(oeeService.getMoByPlIdAndDate(eq("SMT1-1"), any(Date.class), any(Date.class))).thenReturn(mockMoList);

		// 执行测试
		mockMvc
			.perform(get("/oee/mo").param("plId", "SMT1-1")
				.param("startDate", "2025-07-08 08:00:00")
				.param("endDate", "2025-07-09 08:00:00"))
			.andExpect(status().isOk())
			.andExpect(jsonPath("$").isArray())
			.andExpect(jsonPath("$.length()").value(2))
			.andExpect(jsonPath("$[0].mo").value("MO001"))
			.andExpect(jsonPath("$[0].plId").value("SMT1-1"))
			.andExpect(jsonPath("$[1].mo").value("MO002"))
			.andExpect(jsonPath("$[1].plId").value("SMT1-1"));
	}

	@Test
	public void testGetLabelWorkProcessAnalysis() throws Exception {
		// 准备测试数据
		Date testDate = new Date();
		List<LabelWorkProcessAnalysis> mockAnalysisList = Arrays.asList(
				new LabelWorkProcessAnalysis("LB001", "SMT1-1", "MO001", "N", "N", "N", testDate),
				new LabelWorkProcessAnalysis("LB002", "SMT1-1", "MO002", "N", "Y", "N", testDate));

		// 模拟服务层返回
		when(oeeService.getLabelWorkProcessAnalysis(eq("SMT1-1"), any(Date.class), any(Date.class)))
			.thenReturn(mockAnalysisList);

		// 执行测试
		mockMvc
			.perform(get("/oee/label-work-process-analysis").param("plId", "SMT1-1")
				.param("startDate", "2025-07-08 08:00:00")
				.param("endDate", "2025-07-09 08:00:00"))
			.andExpect(status().isOk())
			.andExpect(jsonPath("$").isArray())
			.andExpect(jsonPath("$.length()").value(2))
			.andExpect(jsonPath("$[0].lbId").value("LB001"))
			.andExpect(jsonPath("$[0].plId").value("SMT1-1"))
			.andExpect(jsonPath("$[0].mo").value("MO001"))
			.andExpect(jsonPath("$[0].firstResult").value("N"))
			.andExpect(jsonPath("$[0].keyWpFirstResult").value("N"))
			.andExpect(jsonPath("$[0].finalResult").value("N"))
			.andExpect(jsonPath("$[1].lbId").value("LB002"))
			.andExpect(jsonPath("$[1].keyWpFirstResult").value("Y"));
	}

}
