package com.hongjing.oee_mes.domain;

import java.util.Date;

public class LabelWorkProcessAnalysis {

	private String lbId;

	private String lbGrp;

	private String plId;

	private String mo;

	private String keyWpFirstResult;

	private String finalResult;

	private Date finalWpCmpDate;

	public LabelWorkProcessAnalysis() {
	}

	public LabelWorkProcessAnalysis(String lbId, String lbGrp, String plId, String mo, String keyWpFirstResult,
			String finalResult, Date finalWpCmpDate) {
		this.lbId = lbId;
		this.lbGrp = lbGrp;
		this.plId = plId;
		this.mo = mo;
		this.keyWpFirstResult = keyWpFirstResult;
		this.finalResult = finalResult;
		this.finalWpCmpDate = finalWpCmpDate;
	}

	public String getLbId() {
		return lbId;
	}

	public void setLbId(String lbId) {
		this.lbId = lbId;
	}

	public String getLbGrp() {
		return lbGrp;
	}

	public void setLbGrp(String lbGrp) {
		this.lbGrp = lbGrp;
	}

	public String getPlId() {
		return plId;
	}

	public void setPlId(String plId) {
		this.plId = plId;
	}

	public String getMo() {
		return mo;
	}

	public void setMo(String mo) {
		this.mo = mo;
	}

	public String getKeyWpFirstResult() {
		return keyWpFirstResult;
	}

	public void setKeyWpFirstResult(String keyWpFirstResult) {
		this.keyWpFirstResult = keyWpFirstResult;
	}

	public String getFinalResult() {
		return finalResult;
	}

	public void setFinalResult(String finalResult) {
		this.finalResult = finalResult;
	}

	public Date getFinalWpCmpDate() {
		return finalWpCmpDate;
	}

	public void setFinalWpCmpDate(Date finalWpCmpDate) {
		this.finalWpCmpDate = finalWpCmpDate;
	}

}
