package com.hongjing.oee_mes.domain;

import java.util.Date;

public class LabelWorkProcessAnalysis {

	private String lbId;

	private String plId;

	private String mo;

	private String firstResult;

	private String keyWpFirstResult;

	private String finalResult;

	private Date finalWpCmpDate;

	public LabelWorkProcessAnalysis() {
	}

	public LabelWorkProcessAnalysis(String lbId, String plId, String mo, String firstResult, String keyWpFirstResult,
			String finalResult, Date finalWpCmpDate) {
		this.lbId = lbId;
		this.plId = plId;
		this.mo = mo;
		this.firstResult = firstResult;
		this.keyWpFirstResult = keyWpFirstResult;
		this.finalResult = finalResult;
		this.finalWpCmpDate = finalWpCmpDate;
	}

	public String getLbId() {
		return lbId;
	}

	public void setLbId(String lbId) {
		this.lbId = lbId;
	}

	public String getPlId() {
		return plId;
	}

	public void setPlId(String plId) {
		this.plId = plId;
	}

	public String getMo() {
		return mo;
	}

	public void setMo(String mo) {
		this.mo = mo;
	}

	public String getFirstResult() {
		return firstResult;
	}

	public void setFirstResult(String firstResult) {
		this.firstResult = firstResult;
	}

	public String getKeyWpFirstResult() {
		return keyWpFirstResult;
	}

	public void setKeyWpFirstResult(String keyWpFirstResult) {
		this.keyWpFirstResult = keyWpFirstResult;
	}

	public String getFinalResult() {
		return finalResult;
	}

	public void setFinalResult(String finalResult) {
		this.finalResult = finalResult;
	}

	public Date getFinalWpCmpDate() {
		return finalWpCmpDate;
	}

	public void setFinalWpCmpDate(Date finalWpCmpDate) {
		this.finalWpCmpDate = finalWpCmpDate;
	}

}
