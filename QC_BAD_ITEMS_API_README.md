# QC不良项目查询接口文档

## 接口概述
根据LB_ID列表查询QC不良项目信息，返回对应的制造订单、线体ID、标签ID、不良项目ID、不良点位和扩展字段信息。

## 接口信息
- **URL**: `/oee-mes-server/oee/qc-bad-items`
- **方法**: POST
- **Content-Type**: application/json

## 请求参数
请求体为JSON数组，包含要查询的LB_ID列表：

```json
[
    "38364648-001 51897217",
    "38364648-001 51897218", 
    "38364648-001 51897219",
    "38364648-001 51897220"
]
```

### 参数说明
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| lbIds | Array<String> | 是 | LB_ID列表，用于查询对应的QC不良项目信息 |

## 响应格式
```json
[
    {
        "mo": "MO202507090001",
        "plId": "SMT1-1", 
        "lbId": "38364648-001 51897217",
        "badItemId": "SOLDER_BRIDGE",
        "badPoint": "U1",
        "fieldEx2": "严重"
    },
    {
        "mo": "MO202507090001",
        "plId": "SMT1-1",
        "lbId": "38364648-001 51897218", 
        "badItemId": "MISSING_COMPONENT",
        "badPoint": "R5",
        "fieldEx2": "一般"
    }
]
```

## 响应字段说明
| 字段名 | 类型 | 描述 |
|--------|------|------|
| mo | String | 制造订单号 |
| plId | String | 线体ID |
| lbId | String | 标签ID |
| badItemId | String | 不良项目ID |
| badPoint | String | 不良点位 |
| fieldEx2 | String | 扩展字段2 |

## SQL查询语句
原始查询需求：
```sql
SELECT
    A.MO, A.PL_ID, A.LB_ID, B.BAD_ITEM_ID, B.BAD_POINT, B.FIELD_EX2
FROM
    TB_PM_QC_HD A
    INNER JOIN
    TB_PM_QC_DT B 
   ON A.QC_ID = B.QC_ID
WHERE
    LB_ID in ('38364648-001 51897217','38364648-001 51897218','38364648-001 51897219','38364648-001 51897220');
```

## 实现说明
1. **实体类**: `QcBadItem.java` - 对应TB_PM_QC_HD和TB_PM_QC_DT表的联合查询结果
2. **Repository**: `OeeRepository.getQcBadItemsByLbIds()` - 执行数据库查询
3. **Service**: `OeeService.getQcBadItemsByLbIds()` - 业务逻辑层
4. **Controller**: `OeeController.getQcBadItemsByLbIds()` - REST接口层

## 错误处理
- 请求体格式错误会返回400 Bad Request
- 数据库连接错误会返回500 Internal Server Error
- 空的LB_ID列表会返回空数组

## 测试命令
```bash
curl -X POST "http://localhost:9996/oee-mes-server/oee/qc-bad-items" \
  -H "Content-Type: application/json" \
  -d '[
    "38364648-001 51897217",
    "38364648-001 51897218",
    "38364648-001 51897219", 
    "38364648-001 51897220"
  ]'
```

## 使用场景
- 质量分析：根据标签ID查询对应的不良项目信息
- 缺陷追踪：追踪特定标签的质量问题
- 生产监控：监控特定批次的质量状况
- 报表生成：生成质量相关的统计报表

## 注意事项
1. 使用POST方法是因为需要传递LB_ID列表，避免URL长度限制
2. 支持批量查询，提高查询效率
3. 返回结果按数据库中的顺序返回，如需排序请在前端处理
4. 如果某个LB_ID没有对应的QC记录，不会在结果中出现
