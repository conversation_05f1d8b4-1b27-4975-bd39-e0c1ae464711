package com.hongjing.oee_mes.service;

import java.util.Date;
import java.util.List;

import com.hongjing.oee_mes.domain.Device;
import com.hongjing.oee_mes.domain.LabelWorkProcessAnalysis;
import com.hongjing.oee_mes.domain.Line;
import com.hongjing.oee_mes.domain.Mo;
import com.hongjing.oee_mes.domain.StandardCapacity;

public interface OeeService {

	List<Line> getLineList();

	List<Device> getDeviceListByLine(String code);

	StandardCapacity getStandardCapacity(String prodId, String plId, String bomSide);

	List<Mo> getMoByPlIdAndDate(String plId, Date startDate, Date endDate);

	List<LabelWorkProcessAnalysis> getLabelWorkProcessAnalysis(String plId, Date startDate, Date endDate);

}
