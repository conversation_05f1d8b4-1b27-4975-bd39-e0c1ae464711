package com.hongjing.oee_mes.controller;

import java.util.Collections;
import java.util.List;

// 引入日志框架
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.hongjing.oee_mes.domain.LabelLatestStatus;
import com.hongjing.oee_mes.service.LabelWorkProcessService;

@RestController
@RequestMapping("/label-work-process")
// @Validated 注解不再需要，因为我们不再使用方法参数验证
public class LabelWorkProcessController {

	// 定义一个常量来表示最大允许的列表大小，便于维护
	private static final int MAX_LB_IDS_SIZE = 300;

	// 建议添加日志记录，以便在发生截断时能够知晓
	private static final Logger log = LoggerFactory.getLogger(LabelWorkProcessController.class);

	private final LabelWorkProcessService service;

	public LabelWorkProcessController(LabelWorkProcessService service) {
		this.service = service;
	}

	@PostMapping("/latest-status")
	// 移除 @Size 注解，因为我们要手动处理而不是验证失败
	public List<LabelLatestStatus> getLatestStatusByLbIds(@RequestBody List<String> lbIds) {
		// 1. 检查输入是否为null或空，如果是，则直接返回空列表，避免后续空指针异常
		if (lbIds == null || lbIds.isEmpty()) {
			return Collections.emptyList();
		}

		// 2. 检查列表长度是否超过限制
		if (lbIds.size() > MAX_LB_IDS_SIZE) {
			// 打印一条警告日志，这对于追踪问题和了解系统行为非常重要
			log.warn("请求的 lbIds 列表长度为 {}，超过了 {} 的限制。将自动截取前 {} 个元素进行查询。", lbIds.size(), MAX_LB_IDS_SIZE,
					MAX_LB_IDS_SIZE);

			// 3. 如果超过限制，使用 subList 进行截断，只取前 300 个元素
			List<String> truncatedLbIds = lbIds.subList(0, MAX_LB_IDS_SIZE);
			return service.getLatestStatusByLbIds(truncatedLbIds);
		}

		// 4. 如果没有超过限制，直接使用原始列表进行查询
		return service.getLatestStatusByLbIds(lbIds);
	}

}