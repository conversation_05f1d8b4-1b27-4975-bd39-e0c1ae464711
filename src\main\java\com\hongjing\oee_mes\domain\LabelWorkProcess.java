package com.hongjing.oee_mes.domain;

import java.util.Date;

public class LabelWorkProcess {

	private String lbId;

	private Date wpCmpDate;

	private String isPass;

	public LabelWorkProcess() {
	}

	public LabelWorkProcess(String lbId, Date wpCmpDate, String isPass) {
		this.lbId = lbId;
		this.wpCmpDate = wpCmpDate;
		this.isPass = isPass;
	}

	// Getters and setters
	public String getLbId() {
		return lbId;
	}

	public void setLbId(String lbId) {
		this.lbId = lbId;
	}

	public Date getWpCmpDate() {
		return wpCmpDate;
	}

	public void setWpCmpDate(Date wpCmpDate) {
		this.wpCmpDate = wpCmpDate;
	}

	public String getIsPass() {
		return isPass;
	}

	public void setIsPass(String isPass) {
		this.isPass = isPass;
	}

}