---
server:
  port: 9996
  servlet:
    context-path: /oee-mes-server
spring:
  application:
    name: oee-mes
  datasource:
    url: ******************************************
    username: ims
    password: imsorcl123
    driver-class-name: oracle.jdbc.OracleDriver
    hikari:
      # HikariCP 连接池特定配置
      minimum-idle: 5           # 保持在连接池中的最小空闲连接数
      maximum-pool-size: 20     # 连接池中的最大连接数
      idle-timeout: 600000      # 空闲连接在被移除之前的最大时间（毫秒），默认600000（10分钟）
      max-lifetime: 1800000     # 连接在池中最长时间（毫秒），默认1800000（30分钟）
      connection-timeout: 30000 # 从池中获取连接的最大等待时间（毫秒），默认30000（30秒）
      pool-name: HikariPool-1   # 连接池名字，便于识别和日志记录
  jpa:
    show-sql: true