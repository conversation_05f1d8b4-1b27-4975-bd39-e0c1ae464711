package com.hongjing.oee_mes.service.impl;

import java.util.List;

import org.springframework.stereotype.Service;

import com.hongjing.oee_mes.domain.LabelLatestStatus;
import com.hongjing.oee_mes.repository.LabelWorkProcessRepository;
import com.hongjing.oee_mes.service.LabelWorkProcessService;

@Service
public class LabelWorkProcessServiceImpl implements LabelWorkProcessService {

	private final LabelWorkProcessRepository repository;

	public LabelWorkProcessServiceImpl(LabelWorkProcessRepository repository) {
		this.repository = repository;
	}

	@Override
	public List<LabelLatestStatus> getLatestStatusByLbIds(List<String> lbIds) {
		return repository.getLatestStatusByLbIds(lbIds);
	}

	// @Override
	// @Transactional
	// public void saveLabelWorkProcess(LabelWorkProcess labelWorkProcess) {
	// repository.saveLabelWorkProcess(labelWorkProcess);
	// }

	// @Override
	// @Transactional
	// public void updateLabelWorkProcess(LabelWorkProcess labelWorkProcess) {
	// repository.updateLabelWorkProcess(labelWorkProcess);
	// }

	// @Override
	// @Transactional
	// public void deleteLabelWorkProcess(String lbId, Date wpCmpDate) {
	// repository.deleteLabelWorkProcess(lbId, wpCmpDate);
	// }

}