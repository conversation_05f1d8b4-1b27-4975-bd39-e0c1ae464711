package com.hongjing.oee_mes.repository;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import org.springframework.stereotype.Repository;

import com.hongjing.oee_mes.domain.Device;
import com.hongjing.oee_mes.domain.LabelWorkProcessAnalysis;
import com.hongjing.oee_mes.domain.Line;
import com.hongjing.oee_mes.domain.Mo;
import com.hongjing.oee_mes.domain.StandardCapacity;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;

@Repository
@SuppressWarnings("unchecked")
public class OeeRepository {

	@PersistenceContext
	private EntityManager entityManager;

	public List<Line> getLineList() {
		String sql = "select pl_id code,pl_name name from TB_BS_PL where disable = 'N' order by pl_id ";
		Query query = entityManager.createNativeQuery(sql);
		List<Object[]> resultList = query.getResultList();
		return resultList.stream().map(row -> new Line((String) row[0], (String) row[1])).toList();
	}

	public List<Device> getDeviceListByLine(String code) {
		String sql = "";
		Query query = entityManager.createNativeQuery(sql);
		query.setParameter("code", code);
		List<Object[]> resultList = query.getResultList();
		return resultList.stream().map(row -> new Device((String) row[0], (String) row[1])).toList();
	}

	/**
	 * 获取线体和产品所对应产能
	 * @param prodId 产品ID
	 * @param plId 线体ID
	 */
	public List<StandardCapacity> getStandardCapacity(String prodId, String plId, String bomSide) {
		String sql = "SELECT STD_CT, STD_CY\r\n" + //
				"FROM TB_BS_CY\r\n" + //
				"WHERE PROD_ID = :prodId\r\n" + //
				"  AND PL_ID = :plId\r\n" + //
				"  AND BOM_SIDE = :bomSide";
		Query query = entityManager.createNativeQuery(sql);
		query.setParameter("prodId", prodId);
		query.setParameter("plId", plId);
		query.setParameter("bomSide", bomSide);
		List<Object[]> resultList = query.getResultList();
		return resultList.stream()
			.map(row -> new StandardCapacity(((BigDecimal) row[0]).longValue(), ((BigDecimal) row[1]).longValue()))
			.toList();
	}

	/**
	 * 根据线体ID和日期查询MO信息
	 * @param plId 线体ID
	 * @param startDate 开始日期
	 * @param endDate 结束日期
	 * @return MO列表
	 */
	public List<Mo> getMoByPlIdAndDate(String plId, Date startDate, Date endDate) {
		String sql = "SELECT MO, PL_ID, T_BDATE " + "FROM TB_PP_MO " + "WHERE PL_ID = :plId "
				+ "AND T_BDATE >= :startDate " + "AND T_BDATE < :endDate";
		Query query = entityManager.createNativeQuery(sql);
		query.setParameter("plId", plId);
		query.setParameter("startDate", startDate);
		query.setParameter("endDate", endDate);
		List<Object[]> resultList = query.getResultList();
		return resultList.stream().map(row -> new Mo((String) row[0], (String) row[1], (Date) row[2])).toList();
	}

	/**
	 * 根据线体ID和日期查询标签工艺过程分析数据 查询失败的标签（MIN(IS_PASS) = 'N'）及其相关统计信息
	 * @param plId 线体ID
	 * @param startDate 开始日期
	 * @param endDate 结束日期
	 * @return 标签工艺过程分析列表
	 */
	public List<LabelWorkProcessAnalysis> getLabelWorkProcessAnalysis(String plId, Date startDate, Date endDate) {
		String sql = """
				SELECT
				  LB_ID,
				  PL_ID,
				  MO,
				  MIN(IS_PASS) AS first_result,
				  MIN(
				    CASE
				      WHEN WP_ID IN ('SMT-03', 'SMT-06') THEN IS_PASS
				      ELSE 'Y'
				    END
				  ) AS KEY_WP_FIRST_RESULT,
				  MAX(IS_PASS) KEEP (DENSE_RANK LAST ORDER BY WP_CMP_DATE) AS final_result,
				  MAX(WP_CMP_DATE) KEEP (DENSE_RANK LAST ORDER BY WP_CMP_DATE) AS FINAL_WP_CMP_DATE
				FROM
				  TB_PM_MO_LBWP
				WHERE
				  WP_CMP_DATE >= :startDate
				  AND WP_CMP_DATE < :endDate
				  AND PL_ID = :plId
				GROUP BY
				  LB_ID, PL_ID, MO
				HAVING
				  MIN(IS_PASS) = 'N'
				ORDER BY
				  FINAL_WP_CMP_DATE
				""";

		Query query = entityManager.createNativeQuery(sql);
		query.setParameter("plId", plId);
		query.setParameter("startDate", startDate);
		query.setParameter("endDate", endDate);
		List<Object[]> resultList = query.getResultList();
		return resultList.stream()
			.map(row -> new LabelWorkProcessAnalysis((String) row[0], // LB_ID
					(String) row[1], // PL_ID
					(String) row[2], // MO
					row[3].toString(), // first_result
					row[4].toString(), // KEY_WP_FIRST_RESULT
					row[5].toString(), // final_result
					(Date) row[6] // FINAL_WP_CMP_DATE
			))
			.toList();
	}

}
