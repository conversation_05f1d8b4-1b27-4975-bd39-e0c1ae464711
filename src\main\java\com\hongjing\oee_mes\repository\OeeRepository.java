package com.hongjing.oee_mes.repository;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import org.springframework.stereotype.Repository;

import com.hongjing.oee_mes.domain.Device;
import com.hongjing.oee_mes.domain.LabelWorkProcessAnalysis;
import com.hongjing.oee_mes.domain.Line;
import com.hongjing.oee_mes.domain.Mo;
import com.hongjing.oee_mes.domain.QcBadItem;
import com.hongjing.oee_mes.domain.StandardCapacity;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;

@Repository
@SuppressWarnings("unchecked")
public class OeeRepository {

	@PersistenceContext
	private EntityManager entityManager;

	public List<Line> getLineList() {
		String sql = "select pl_id code,pl_name name from TB_BS_PL where disable = 'N' order by pl_id ";
		Query query = entityManager.createNativeQuery(sql);
		List<Object[]> resultList = query.getResultList();
		return resultList.stream().map(row -> new Line((String) row[0], (String) row[1])).toList();
	}

	public List<Device> getDeviceListByLine(String code) {
		String sql = "";
		Query query = entityManager.createNativeQuery(sql);
		query.setParameter("code", code);
		List<Object[]> resultList = query.getResultList();
		return resultList.stream().map(row -> new Device((String) row[0], (String) row[1])).toList();
	}

	/**
	 * 获取线体和产品所对应产能
	 * @param prodId 产品ID
	 * @param plId 线体ID
	 */
	public List<StandardCapacity> getStandardCapacity(String prodId, String plId, String bomSide) {
		String sql = "SELECT STD_CT, STD_CY\r\n" + //
				"FROM TB_BS_CY\r\n" + //
				"WHERE PROD_ID = :prodId\r\n" + //
				"  AND PL_ID = :plId\r\n" + //
				"  AND BOM_SIDE = :bomSide";
		Query query = entityManager.createNativeQuery(sql);
		query.setParameter("prodId", prodId);
		query.setParameter("plId", plId);
		query.setParameter("bomSide", bomSide);
		List<Object[]> resultList = query.getResultList();
		return resultList.stream()
			.map(row -> new StandardCapacity(((BigDecimal) row[0]).longValue(), ((BigDecimal) row[1]).longValue()))
			.toList();
	}

	/**
	 * 根据线体ID和日期查询MO信息
	 * @param plId 线体ID
	 * @param startDate 开始日期
	 * @param endDate 结束日期
	 * @return MO列表
	 */
	public List<Mo> getMoByPlIdAndDate(String plId, Date startDate, Date endDate) {
		String sql = "SELECT MO, PL_ID, T_BDATE " + "FROM TB_PP_MO " + "WHERE PL_ID = :plId "
				+ "AND T_BDATE >= :startDate " + "AND T_BDATE < :endDate";
		Query query = entityManager.createNativeQuery(sql);
		query.setParameter("plId", plId);
		query.setParameter("startDate", startDate);
		query.setParameter("endDate", endDate);
		List<Object[]> resultList = query.getResultList();
		return resultList.stream().map(row -> new Mo((String) row[0], (String) row[1], (Date) row[2])).toList();
	}

	/**
	 * 根据线体ID和日期查询标签工艺过程分析数据 查询失败的标签（MIN(IS_PASS) = 'N'）及其相关统计信息
	 * @param plId 线体ID
	 * @param startDate 开始日期
	 * @param endDate 结束日期
	 * @return 标签工艺过程分析列表
	 */
	public List<LabelWorkProcessAnalysis> getLabelWorkProcessAnalysis(String plId, Date startDate, Date endDate) {
		String sql = """
				SELECT
				    A.LB_ID,
				    B.LB_GRP,
				    A.PL_ID,
				    A.MO,
				    MIN(
				        CASE
				            WHEN A.WP_ID IN ('SMT-03', 'SMT-06')
				            THEN A.IS_PASS
				            ELSE 'Y'
				        END
				    ) AS KEY_WP_FIRST_RESULT,
				    MAX(A.IS_PASS) KEEP (DENSE_RANK LAST ORDER BY A.WP_CMP_DATE) AS FINAL_RESULT,
				    MAX(A.WP_CMP_DATE) KEEP (DENSE_RANK LAST ORDER BY A.WP_CMP_DATE) AS FINAL_WP_CMP_DATE
				FROM
				    TB_PM_MO_LBWP A
				INNER JOIN TB_PM_MO_LB B
				ON A.LB_ID = B.LB_ID
				WHERE
				    A.WP_CMP_DATE >= :startDate
				    AND A.WP_CMP_DATE < :endDate
				    AND A.PL_ID = :plId
				GROUP BY
				    A.LB_ID,
				    B.LB_GRP,
				    A.PL_ID,
				    A.MO
				HAVING
				     MIN(
				        CASE
				            WHEN A.WP_ID IN ('SMT-03', 'SMT-06')
				            THEN A.IS_PASS
				            ELSE 'Y'
				        END
				    ) = 'N'
				ORDER BY
				    FINAL_WP_CMP_DATE
				""";

		Query query = entityManager.createNativeQuery(sql);
		query.setParameter("plId", plId);
		query.setParameter("startDate", startDate);
		query.setParameter("endDate", endDate);
		List<Object[]> resultList = query.getResultList();
		return resultList.stream()
			.map(row -> new LabelWorkProcessAnalysis((String) row[0], // LB_ID
					(String) row[1], // LB_GRP
					(String) row[2], // PL_ID
					(String) row[3], // MO
					row[4].toString(), // KEY_WP_FIRST_RESULT
					row[5].toString(), // final_result
					(Date) row[6] // FINAL_WP_CMP_DATE
			))
			.toList();
	}

	/**
	 * 根据LB_ID列表查询QC不良项目信息
	 * @param lbIds LB_ID列表
	 * @return QC不良项目列表
	 */
	public List<QcBadItem> getQcBadItemsByLbIds(List<String> lbIds) {
		String sql = """
				SELECT
				    A.MO, A.PL_ID, A.LB_ID, B.BAD_ITEM_ID, B.BAD_POINT, B.FIELD_EX2
				FROM
				    TB_PM_QC_HD A
				    INNER JOIN
				    TB_PM_QC_DT B
				   ON A.QC_ID = B.QC_ID
				WHERE
				    A.LB_ID IN (:lbIds)
				""";
		Query query = entityManager.createNativeQuery(sql);
		query.setParameter("lbIds", lbIds);
		List<Object[]> resultList = query.getResultList();
		return resultList.stream()
			.map(row -> new QcBadItem((String) row[0], // MO
					(String) row[1], // PL_ID
					(String) row[2], // LB_ID
					(String) row[3], // BAD_ITEM_ID
					(String) row[4], // BAD_POINT
					(String) row[5] // FIELD_EX2
			))
			.toList();
	}

}
