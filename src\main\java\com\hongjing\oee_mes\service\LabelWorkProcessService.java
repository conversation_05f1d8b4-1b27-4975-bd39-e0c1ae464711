package com.hongjing.oee_mes.service;

import java.util.List;

import com.hongjing.oee_mes.domain.LabelLatestStatus;

public interface LabelWorkProcessService {

	List<LabelLatestStatus> getLatestStatusByLbIds(List<String> lbIds);
	// void saveLabelWorkProcess(LabelWorkProcess labelWorkProcess);
	// void updateLabelWorkProcess(LabelWorkProcess labelWorkProcess);
	// void deleteLabelWorkProcess(String lbId, Date wpCmpDate);

}