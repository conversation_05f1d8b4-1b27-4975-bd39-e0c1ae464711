package com.hongjing.oee_mes.controller;

import java.util.Collections;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.hongjing.oee_mes.service.SampleLabelWorkProcessService;

@RestController
@RequestMapping("/sample")
public class SampleLabelWorkProcessController {

	private static final int MAX_LB_IDS_SIZE = 300;

	private static final Logger log = LoggerFactory.getLogger(SampleLabelWorkProcessController.class);

	private final SampleLabelWorkProcessService service;

	public SampleLabelWorkProcessController(SampleLabelWorkProcessService service) {
		this.service = service;
	}

	@PostMapping("/ids-by-lbids")
	public List<String> getIdsByLbIds(@RequestBody List<String> lbIds) {
		if (lbIds == null || lbIds.isEmpty()) {
			return Collections.emptyList();
		}
		if (lbIds.size() > MAX_LB_IDS_SIZE) {
			log.warn("请求的 lbIds 列表长度为 {}，超过了 {} 的限制。将自动截取前 {} 个元素进行查询。", lbIds.size(), MAX_LB_IDS_SIZE,
					MAX_LB_IDS_SIZE);
			lbIds = lbIds.subList(0, MAX_LB_IDS_SIZE);
		}
		return service.getIdsByLbIds(lbIds);
	}

}
