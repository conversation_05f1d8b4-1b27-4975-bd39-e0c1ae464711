package com.hongjing.oee_mes.repository;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import org.springframework.stereotype.Repository;

import java.util.Collections; // 引入 Collections
import java.util.List;

@Repository
@SuppressWarnings("unchecked")
public class SampleLabelWorkProcessRepository {

	@PersistenceContext
	private EntityManager entityManager;

	/**
	 * 根据 LB_ID 列表，查询对应的、不重复的 ID 列表。
	 * @param lbIds LB_ID 列表
	 * @return 返回查询到的唯一 ID 列表
	 */
	public List<String> getIdsByLbIds(List<String> lbIds) {
		// 最佳实践：处理传入列表为空的情况，避免数据库执行无效查询
		if (lbIds == null || lbIds.isEmpty()) {
			return Collections.emptyList(); // 直接返回一个空列表
		}

		// 1. 定义原生 SQL 查询语句，加入 DISTINCT 关键字
		String sql = """
				SELECT DISTINCT
				    LB_ID
				FROM
				    TB_HJ_PM_SAMPLE_LBWP
				WHERE
				    LB_ID IN (:lbIds)
				""";

		// 2. 创建查询并设置参数
		var query = entityManager.createNativeQuery(sql, String.class);
		query.setParameter("lbIds", lbIds);

		// 3. 执行查询并返回结果
		return query.getResultList();
	}

}